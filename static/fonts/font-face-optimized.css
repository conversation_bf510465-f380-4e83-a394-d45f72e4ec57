/* Optimized Font Face Declarations with Subsets and Lazy Loading */

/* Critical Fonts - Loaded Immediately */
@font-face {
  font-family: "MatrixBoldSmallCaps";
  src: url("MatrixBoldSmallCaps.ttf") format("truetype");
  font-weight: bold;
  font-display: swap;
  font-loading: critical;
}

/* High Priority Fonts - Preloaded */
@font-face {
  font-family: "zh";
  src: url("optimized/zh-subset.ttf") format("truetype");
  font-display: swap;
  font-loading: high-priority;
  unicode-range: U+0020-007F; /* Basic Latin */
}

@font-face {
  font-family: "en";
  src: url("optimized/en-subset.ttf") format("truetype");
  font-display: swap;
  font-loading: high-priority;
  unicode-range: U+0020-007F; /* Basic Latin */
}

/* Medium Priority Fonts - Lazy Loaded */
@font-face {
  font-family: "cn";
  src: url("cn.ttf") format("truetype");
  font-display: swap;
  font-loading: lazy;
}

@font-face {
  font-family: "jp";
  src: url("jp.ttf") format("truetype");
  font-display: swap;
  font-loading: lazy;
}

@font-face {
  font-family: "en2";
  src: url("en2.ttf") format("truetype");
  font-display: swap;
  font-loading: lazy;
}

/* Low Priority Fonts - Loaded on Demand */
@font-face {
  font-family: "jp2";
  src: url("jp2.otf") format("truetype");
  font-display: optional;
  font-loading: on-demand;
}

@font-face {
  font-family: "en3";
  src: url("en3.ttf") format("truetype");
  font-display: optional;
  font-loading: on-demand;
}

/* Special Fonts */
@font-face {
  font-family: "link";
  src: url("link.ttf") format("truetype");
  font-display: swap;
  font-loading: lazy;
}
