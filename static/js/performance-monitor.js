
/**
 * Performance Monitoring for Yu-Gi-Oh Card Maker
 * Real-time performance tracking and Core Web Vitals measurement
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {}
    this.observers = []
    this.startTime = performance.now()
    this.init()
  }

  /**
   * Initialize performance monitoring
   */
  init() {
    // Measure Core Web Vitals
    this.measureLCP()
    this.measureFID()
    this.measureCLS()
    
    // Measure additional metrics
    this.measureFCP()
    this.measureTTI()
    this.measureResourceTiming()
    
    // Monitor ongoing performance
    this.setupPerformanceObserver()
    
    // Report metrics when page is about to unload
    this.setupReporting()
  }

  /**
   * Measure Largest Contentful Paint (LCP)
   */
  measureLCP() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      
      this.metrics.LCP = {
        value: lastEntry.startTime,
        element: lastEntry.element,
        timestamp: Date.now()
      }
      
      console.log('LCP:', this.metrics.LCP.value, 'ms')
    })
    
    observer.observe({ entryTypes: ['largest-contentful-paint'] })
    this.observers.push(observer)
  }

  /**
   * Measure First Input Delay (FID)
   */
  measureFID() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        this.metrics.FID = {
          value: entry.processingStart - entry.startTime,
          timestamp: Date.now()
        }
        
        console.log('FID:', this.metrics.FID.value, 'ms')
      })
    })
    
    observer.observe({ entryTypes: ['first-input'] })
    this.observers.push(observer)
  }

  /**
   * Measure Cumulative Layout Shift (CLS)
   */
  measureCLS() {
    if (!('PerformanceObserver' in window)) return

    let clsValue = 0
    let sessionValue = 0
    let sessionEntries = []

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      
      entries.forEach((entry) => {
        if (!entry.hadRecentInput) {
          const firstSessionEntry = sessionEntries[0]
          const lastSessionEntry = sessionEntries[sessionEntries.length - 1]
          
          if (sessionValue && 
              entry.startTime - lastSessionEntry.startTime < 1000 &&
              entry.startTime - firstSessionEntry.startTime < 5000) {
            sessionValue += entry.value
            sessionEntries.push(entry)
          } else {
            sessionValue = entry.value
            sessionEntries = [entry]
          }
          
          if (sessionValue > clsValue) {
            clsValue = sessionValue
            this.metrics.CLS = {
              value: clsValue,
              timestamp: Date.now()
            }
            
            console.log('CLS:', this.metrics.CLS.value)
          }
        }
      })
    })
    
    observer.observe({ entryTypes: ['layout-shift'] })
    this.observers.push(observer)
  }

  /**
   * Measure First Contentful Paint (FCP)
   */
  measureFCP() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        if (entry.name === 'first-contentful-paint') {
          this.metrics.FCP = {
            value: entry.startTime,
            timestamp: Date.now()
          }
          
          console.log('FCP:', this.metrics.FCP.value, 'ms')
        }
      })
    })
    
    observer.observe({ entryTypes: ['paint'] })
    this.observers.push(observer)
  }

  /**
   * Measure Time to Interactive (TTI)
   */
  measureTTI() {
    // TTI is complex to measure accurately, using a simplified approach
    const checkTTI = () => {
      if (document.readyState === 'complete') {
        const navigationStart = performance.timing.navigationStart
        const loadEventEnd = performance.timing.loadEventEnd
        
        this.metrics.TTI = {
          value: loadEventEnd - navigationStart,
          timestamp: Date.now()
        }
        
        console.log('TTI (estimated):', this.metrics.TTI.value, 'ms')
      } else {
        setTimeout(checkTTI, 100)
      }
    }
    
    checkTTI()
  }

  /**
   * Measure resource timing
   */
  measureResourceTiming() {
    window.addEventListener('load', () => {
      const resources = performance.getEntriesByType('resource')
      
      let totalSize = 0
      let jsSize = 0
      let cssSize = 0
      let imageSize = 0
      let fontSize = 0
      
      resources.forEach((resource) => {
        const size = resource.transferSize || 0
        totalSize += size
        
        if (resource.name.includes('.js')) {
          jsSize += size
        } else if (resource.name.includes('.css')) {
          cssSize += size
        } else if (resource.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) {
          imageSize += size
        } else if (resource.name.match(/\.(ttf|otf|woff|woff2)$/)) {
          fontSize += size
        }
      })
      
      this.metrics.resourceTiming = {
        totalSize: Math.round(totalSize / 1024), // KB
        jsSize: Math.round(jsSize / 1024),
        cssSize: Math.round(cssSize / 1024),
        imageSize: Math.round(imageSize / 1024),
        fontSize: Math.round(fontSize / 1024),
        requestCount: resources.length,
        timestamp: Date.now()
      }
      
      console.log('Resource Timing:', this.metrics.resourceTiming)
    })
  }

  /**
   * Setup performance observer for ongoing monitoring
   */
  setupPerformanceObserver() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        // Log long tasks that might affect performance
        if (entry.entryType === 'longtask') {
          console.warn('Long task detected:', entry.duration, 'ms')
        }
      })
    })
    
    try {
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.push(observer)
    } catch (e) {
      // Long task API not supported
    }
  }

  /**
   * Setup performance reporting
   */
  setupReporting() {
    // Report metrics before page unload
    window.addEventListener('beforeunload', () => {
      this.reportMetrics()
    })
    
    // Report metrics after 10 seconds
    setTimeout(() => {
      this.reportMetrics()
    }, 10000)
  }

  /**
   * Report collected metrics
   */
  reportMetrics() {
    const report = {
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      metrics: this.metrics,
      performance: {
        navigation: performance.timing,
        memory: performance.memory
      }
    }
    
    // Send to analytics or logging service
    console.log('Performance Report:', report)
    
    // You can send this data to your analytics service
    // this.sendToAnalytics(report)
  }

  /**
   * Send metrics to analytics service
   */
  sendToAnalytics(data) {
    if (navigator.sendBeacon) {
      navigator.sendBeacon('/api/performance', JSON.stringify(data))
    } else {
      fetch('/api/performance', {
        method: 'POST',
        body: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json'
        }
      }).catch(() => {
        // Ignore errors in performance reporting
      })
    }
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return this.metrics
  }

  /**
   * Cleanup observers
   */
  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Global performance monitor instance
window.performanceMonitor = new PerformanceMonitor()

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (window.performanceMonitor) {
    window.performanceMonitor.cleanup()
  }
})
