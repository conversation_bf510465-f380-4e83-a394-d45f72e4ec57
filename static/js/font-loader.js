
/**
 * Progressive Font Loading for Yu-Gi-Oh Card Maker
 * Implements smart font loading based on user language and usage patterns
 */

class FontLoader {
  constructor() {
    this.loadedFonts = new Set()
    this.currentLanguage = 'en'
    this.fontLoadPromises = new Map()
  }

  /**
   * Initialize font loading based on current language
   */
  async init(language = 'en') {
    this.currentLanguage = language
    
    // Always load critical fonts first
    await this.loadCriticalFonts()
    
    // Load language-specific fonts
    await this.loadLanguageFonts(language)
    
    // Preload likely next fonts
    this.preloadLikelyFonts(language)
  }

  /**
   * Load critical fonts immediately
   */
  async loadCriticalFonts() {
    const criticalFonts = ['MatrixBoldSmallCaps', 'en']
    
    for (const fontFamily of criticalFonts) {
      await this.loadFont(fontFamily, 'critical')
    }
  }

  /**
   * Load fonts for specific language
   */
  async loadLanguageFonts(language) {
    const languageFontMap = {
      'zh': ['zh', 'cn'],
      'ja': ['jp', 'jp2'], 
      'en': ['en', 'en2'],
      'ko': ['jp'], // Korean can use Japanese fonts as fallback
      'de': ['en', 'en2'],
      'fr': ['en', 'en2'],
      'pt': ['en', 'en2'],
      'es': ['en', 'en2'],
      'el': ['en', 'en2'],
      'th': ['en', 'en2'],
      'ru': ['en', 'en2'],
      'vi': ['en', 'en2']
    }

    const fontsToLoad = languageFontMap[language] || ['en']
    
    for (const fontFamily of fontsToLoad) {
      await this.loadFont(fontFamily, 'high-priority')
    }
  }

  /**
   * Load a specific font with priority
   */
  async loadFont(fontFamily, priority = 'normal') {
    if (this.loadedFonts.has(fontFamily)) {
      return Promise.resolve()
    }

    if (this.fontLoadPromises.has(fontFamily)) {
      return this.fontLoadPromises.get(fontFamily)
    }

    const loadPromise = new Promise((resolve, reject) => {
      if (!document.fonts) {
        // Fallback for browsers without Font Loading API
        resolve()
        return
      }

      const font = new FontFace(fontFamily, `url('/fonts/${fontFamily}.ttf')`)
      
      font.load().then(() => {
        document.fonts.add(font)
        this.loadedFonts.add(fontFamily)
        console.log(`✅ Font loaded: ${fontFamily}`)
        resolve()
      }).catch((error) => {
        console.warn(`⚠️  Failed to load font: ${fontFamily}`, error)
        resolve() // Don't reject to avoid breaking the app
      })
    })

    this.fontLoadPromises.set(fontFamily, loadPromise)
    return loadPromise
  }

  /**
   * Preload fonts that are likely to be needed
   */
  preloadLikelyFonts(currentLanguage) {
    const likelyLanguages = this.getLikelyNextLanguages(currentLanguage)
    
    // Use requestIdleCallback for non-critical preloading
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        likelyLanguages.forEach(lang => {
          this.loadLanguageFonts(lang)
        })
      })
    }
  }

  /**
   * Get likely next languages based on current language
   */
  getLikelyNextLanguages(currentLanguage) {
    const languageGroups = {
      'en': ['zh', 'ja'],
      'zh': ['en', 'ja'],
      'ja': ['en', 'zh'],
      'ko': ['ja', 'zh'],
      'de': ['en', 'fr'],
      'fr': ['en', 'de'],
      'pt': ['es', 'en'],
      'es': ['pt', 'en'],
      'el': ['en'],
      'th': ['en'],
      'ru': ['en'],
      'vi': ['en']
    }

    return languageGroups[currentLanguage] || ['en']
  }

  /**
   * Check if font is loaded
   */
  isFontLoaded(fontFamily) {
    return this.loadedFonts.has(fontFamily)
  }

  /**
   * Get loading status
   */
  getLoadingStatus() {
    return {
      loadedFonts: Array.from(this.loadedFonts),
      currentLanguage: this.currentLanguage,
      totalLoaded: this.loadedFonts.size
    }
  }
}

// Global font loader instance
window.fontLoader = new FontLoader()

// Auto-initialize on DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.fontLoader.init()
  })
} else {
  window.fontLoader.init()
}
