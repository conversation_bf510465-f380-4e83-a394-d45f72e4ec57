
/**
 * Lazy Loading for Images - Yu-Gi-Oh Card Maker
 * Implements progressive image loading with WebP support
 */

class ImageLoader {
  constructor() {
    this.observer = null
    this.supportsWebP = false
    this.loadedImages = new Set()
    this.init()
  }

  /**
   * Initialize the image loader
   */
  async init() {
    // Check WebP support
    this.supportsWebP = await this.checkWebPSupport()
    
    // Initialize Intersection Observer for lazy loading
    this.initIntersectionObserver()
    
    // Load critical images immediately
    this.loadCriticalImages()
    
    // Observe all lazy images
    this.observeLazyImages()
  }

  /**
   * Check if browser supports WebP
   */
  checkWebPSupport() {
    return new Promise((resolve) => {
      const webP = new Image()
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2)
      }
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
    })
  }

  /**
   * Initialize Intersection Observer
   */
  initIntersectionObserver() {
    if (!('IntersectionObserver' in window)) {
      // Fallback for older browsers
      this.loadAllImages()
      return
    }

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadImage(entry.target)
          this.observer.unobserve(entry.target)
        }
      })
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    })
  }

  /**
   * Load critical images immediately
   */
  loadCriticalImages() {
    const criticalImages = document.querySelectorAll('img[data-critical="true"]')
    criticalImages.forEach(img => this.loadImage(img))
  }

  /**
   * Observe lazy images
   */
  observeLazyImages() {
    const lazyImages = document.querySelectorAll('img[data-lazy="true"]')
    lazyImages.forEach(img => {
      if (this.observer) {
        this.observer.observe(img)
      } else {
        this.loadImage(img)
      }
    })
  }

  /**
   * Load a specific image
   */
  loadImage(img) {
    if (this.loadedImages.has(img)) return

    const src = this.getOptimalImageSrc(img)
    
    // Create a new image to preload
    const imageLoader = new Image()
    
    imageLoader.onload = () => {
      img.src = src
      img.classList.add('loaded')
      this.loadedImages.add(img)
    }
    
    imageLoader.onerror = () => {
      // Fallback to original src
      const fallbackSrc = img.dataset.fallback || img.dataset.src
      if (fallbackSrc) {
        img.src = fallbackSrc
        img.classList.add('loaded')
      }
    }
    
    imageLoader.src = src
  }

  /**
   * Get optimal image source based on device and format support
   */
  getOptimalImageSrc(img) {
    const baseSrc = img.dataset.src
    const webpSrc = img.dataset.webp
    
    // Use WebP if supported and available
    if (this.supportsWebP && webpSrc) {
      return webpSrc
    }
    
    // Get responsive size
    const width = img.offsetWidth || img.dataset.width || 400
    const responsiveSrc = this.getResponsiveSrc(baseSrc, width)
    
    return responsiveSrc || baseSrc
  }

  /**
   * Get responsive image source
   */
  getResponsiveSrc(baseSrc, width) {
    if (!baseSrc) return null
    
    const ext = baseSrc.split('.').pop()
    const base = baseSrc.replace(new RegExp('\.' + ext + '$'), '')
    
    if (width <= 400) {
      return `${base}-sm.${ext}`
    } else if (width <= 600) {
      return `${base}-md.${ext}`
    }
    
    return baseSrc
  }

  /**
   * Load all images (fallback)
   */
  loadAllImages() {
    const allImages = document.querySelectorAll('img[data-src]')
    allImages.forEach(img => this.loadImage(img))
  }

  /**
   * Preload images for next language
   */
  preloadLanguageImages(language) {
    const imagesToPreload = [
      `/images/card/${language}/Normal.png`,
      `/images/card/${language}/Effect.png`,
      `/images/card/${language}/Spell.png`,
      `/images/card/${language}/Trap.png`
    ]
    
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        imagesToPreload.forEach(src => {
          const img = new Image()
          img.src = src
        })
      })
    }
  }
}

// Global image loader instance
window.imageLoader = new ImageLoader()

// Auto-initialize on DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.imageLoader.init()
  })
} else {
  window.imageLoader.init()
}
