
/**
 * Non-Critical CSS Loader for Yu-Gi-Oh Card Maker
 * Loads non-critical styles asynchronously to improve initial page load
 */

class CSSLoader {
  constructor() {
    this.loadedStyles = new Set()
    this.loadPromises = new Map()
  }

  /**
   * Load CSS file asynchronously
   */
  async loadCSS(href, media = 'all') {
    if (this.loadedStyles.has(href)) {
      return Promise.resolve()
    }

    if (this.loadPromises.has(href)) {
      return this.loadPromises.get(href)
    }

    const loadPromise = new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = href
      link.media = media
      
      link.onload = () => {
        this.loadedStyles.add(href)
        resolve()
      }
      
      link.onerror = () => {
        reject(new Error(`Failed to load CSS: ${href}`))
      }
      
      document.head.appendChild(link)
    })

    this.loadPromises.set(href, loadPromise)
    return loadPromise
  }

  /**
   * Load multiple CSS files
   */
  async loadMultipleCSS(hrefs) {
    const promises = hrefs.map(href => this.loadCSS(href))
    return Promise.all(promises)
  }

  /**
   * Load CSS when element becomes visible
   */
  loadCSSOnVisible(selector, cssHref) {
    const element = document.querySelector(selector)
    if (!element) return

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadCSS(cssHref)
          observer.unobserve(element)
        }
      })
    }, {
      rootMargin: '100px 0px',
      threshold: 0.01
    })

    observer.observe(element)
  }

  /**
   * Initialize non-critical CSS loading
   */
  init() {
    // Load non-critical styles after initial render
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        this.loadNonCriticalStyles()
      })
    } else {
      setTimeout(() => {
        this.loadNonCriticalStyles()
      }, 100)
    }

    // Load section-specific styles on demand
    this.setupSectionLoading()
  }

  /**
   * Load non-critical styles
   */
  loadNonCriticalStyles() {
    const nonCriticalStyles = [
      '/css/animations.css',
      '/css/features.css',
      '/css/footer.css'
    ]

    this.loadMultipleCSS(nonCriticalStyles).catch(error => {
      console.warn('Failed to load some non-critical styles:', error)
    })
  }

  /**
   * Setup section-specific CSS loading
   */
  setupSectionLoading() {
    // Load features section CSS when it becomes visible
    this.loadCSSOnVisible('#features', '/css/features.css')
    
    // Load FAQ section CSS when it becomes visible
    this.loadCSSOnVisible('#faq', '/css/faq.css')
    
    // Load footer CSS when it becomes visible
    this.loadCSSOnVisible('footer', '/css/footer.css')
  }
}

// Global CSS loader instance
window.cssLoader = new CSSLoader()

// Auto-initialize on DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.cssLoader.init()
  })
} else {
  window.cssLoader.init()
}
