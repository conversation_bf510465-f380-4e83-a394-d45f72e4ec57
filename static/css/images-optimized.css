
/* Optimized Image Styles for Yu-Gi-Oh Card Maker */

/* Base image styles */
img {
  max-width: 100%;
  height: auto;
}

/* Lazy loading states */
img[data-lazy="true"] {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

img[data-lazy="true"].loaded {
  opacity: 1;
}

/* Card template images */
.card-template {
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* Responsive card images */
@media (max-width: 576px) {
  .card-template {
    max-width: 400px;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .card-template {
    max-width: 600px;
  }
}

@media (min-width: 769px) {
  .card-template {
    max-width: 1000px;
  }
}

/* Attribute icons */
.attr-icon {
  width: 90px;
  height: 90px;
  object-fit: contain;
}

@media (max-width: 576px) {
  .attr-icon {
    width: 45px;
    height: 45px;
  }
}

/* Loading placeholder */
.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* WebP support detection */
.webp .card-template[data-webp] {
  content: attr(data-webp);
}

.no-webp .card-template[data-fallback] {
  content: attr(data-fallback);
}
