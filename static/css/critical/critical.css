
/* Critical CSS for Yu-Gi-Oh Card Maker - Above the fold styles */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  word-spacing: 1px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

body {
  margin: 0;
  padding: 0;
  background-color: #1a1a2e;
  color: #ffffff;
  line-height: 1.6;
}

/* Navigation - Critical for first paint */
.main-navbar {
  background: linear-gradient(135deg, #16213e 0%, #0f3460 100%);
  border-bottom: 2px solid #e74c3c;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1030;
}

.navbar-brand-custom {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #ffffff !important;
}

.brand-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-logo {
  width: 50px;
  height: 50px;
  object-fit: contain;
}

.brand-title {
  margin: 0;
  font-weight: 700;
}

.brand-main {
  font-size: 1.5rem;
  color: #ffffff;
}

.brand-sub {
  font-size: 0.8rem;
  color: #bdc3c7;
  font-weight: 400;
}

/* Card Editor Section - Critical for main functionality */
.card-editor-section {
  padding-top: 100px;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.preview-panel {
  padding: 1rem;
}

.preview-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-display {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.card-canvas {
  width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.editor-panel {
  padding: 1rem;
}

.editor-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Form styles - Critical for user interaction */
.form-control {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  color: #ffffff;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

label {
  color: #ecf0f1;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

/* Button styles - Critical for actions */
.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-info {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: #ffffff;
}

.btn-info:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: #ffffff;
}

.btn-success:hover {
  background: linear-gradient(135deg, #229954, #1e8449);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
}

.btn-outline-danger {
  background: transparent;
  border: 2px solid #e74c3c;
  color: #e74c3c;
}

.btn-outline-danger:hover {
  background: #e74c3c;
  color: #ffffff;
  transform: translateY(-2px);
}

/* Loading states - Critical for UX */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(26, 26, 46, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design - Critical for mobile */
@media (max-width: 991px) {
  .card-editor-section {
    padding-top: 80px;
  }
  
  .brand-logo {
    width: 40px;
    height: 40px;
  }
  
  .brand-main {
    font-size: 1.2rem;
  }
  
  .brand-sub {
    font-size: 0.7rem;
  }
  
  .preview-container,
  .editor-container {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .preview-container,
  .editor-container {
    padding: 0.75rem;
    margin: 0.5rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Font loading optimization */
@font-face {
  font-family: 'MatrixBoldSmallCaps';
  src: url('/fonts/MatrixBoldSmallCaps.ttf') format('truetype');
  font-weight: bold;
  font-display: swap;
}

/* Utility classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.text-center { text-align: center !important; }
.mb-0 { margin-bottom: 0 !important; }
.me-1 { margin-right: 0.25rem !important; }
.my-3 { margin-top: 1rem !important; margin-bottom: 1rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
