
// Cache configuration for Nuxt.js
export default {
  // Static asset caching
  static: {
    maxAge: 1000 * 60 * 60 * 24 * 365, // 1 year
    setHeaders(res, path) {
      if (path.includes('/fonts/')) {
        res.setHeader('Cache-Control', 'public, max-age=31536000, immutable')
      } else if (path.includes('/images/card/') || path.includes('/images/attr/')) {
        res.setHeader('Cache-Control', 'public, max-age=2592000') // 30 days
      } else if (path.includes('/css/') || path.includes('/js/')) {
        res.setHeader('Cache-Control', 'public, max-age=604800') // 7 days
      }
    }
  },
  
  // PWA configuration
  pwa: {
    workbox: {
      enabled: true,
      swDest: 'sw.js',
      runtimeCaching: [
        {
          urlPattern: /^https:\/\/fonts\.(googleapis|gstatic)\.com/,
          handler: 'CacheFirst',
          options: {
            cacheName: 'google-fonts',
            expiration: {
              maxEntries: 30,
              maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
            }
          }
        },
        {
          urlPattern: /\/_nuxt\//,
          handler: 'CacheFirst',
          options: {
            cacheName: 'nuxt-assets',
            expiration: {
              maxEntries: 100,
              maxAgeSeconds: 60 * 60 * 24 * 7 // 1 week
            }
          }
        }
      ]
    }
  }
}
