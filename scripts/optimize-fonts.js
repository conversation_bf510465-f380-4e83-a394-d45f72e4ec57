#!/usr/bin/env node

/**
 * Font Optimization Script for Yu-Gi-Oh Card Maker
 * This script optimizes large font files by creating subsets and implementing lazy loading
 */

const fs = require('fs')
const path = require('path')

const STATIC_DIR = path.join(__dirname, '..', 'static')
const FONTS_DIR = path.join(STATIC_DIR, 'fonts')
const OPTIMIZED_FONTS_DIR = path.join(FONTS_DIR, 'optimized')

console.log('🔤 Starting font optimization...')

/**
 * Font configuration for optimization
 */
const FONT_CONFIG = {
  'zh.ttf': {
    name: 'Chinese Traditional',
    subset: 'chinese-traditional',
    priority: 'high', // Used for Chinese interface
    commonChars: '一二三四五六七八九十百千万亿元年月日时分秒上下左右前后中大小多少好坏新旧高低长短快慢'
  },
  'cn.ttf': {
    name: 'Chinese Simplified', 
    subset: 'chinese-simplified',
    priority: 'medium',
    commonChars: '一二三四五六七八九十百千万亿元年月日时分秒上下左右前后中大小多少好坏新旧高低长短快慢'
  },
  'jp.ttf': {
    name: 'Japanese',
    subset: 'japanese',
    priority: 'medium',
    commonChars: 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわをん'
  },
  'jp2.otf': {
    name: 'Japanese Extended',
    subset: 'japanese-extended', 
    priority: 'low',
    commonChars: 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわをん'
  },
  'en.ttf': {
    name: 'English',
    subset: 'latin',
    priority: 'high', // Used for default interface
    commonChars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  },
  'en2.ttf': {
    name: 'English Extended',
    subset: 'latin-extended',
    priority: 'medium',
    commonChars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  },
  'en3.ttf': {
    name: 'English Alternative',
    subset: 'latin-alt',
    priority: 'low',
    commonChars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  }
}

/**
 * Create optimized fonts directory
 */
function createOptimizedFontsDir() {
  if (!fs.existsSync(OPTIMIZED_FONTS_DIR)) {
    fs.mkdirSync(OPTIMIZED_FONTS_DIR, { recursive: true })
    console.log('✅ Created optimized fonts directory')
  }
}

/**
 * Generate font subset configuration
 */
function generateFontSubsetConfig(fontFile, config) {
  const fontPath = path.join(FONTS_DIR, fontFile)
  const outputPath = path.join(OPTIMIZED_FONTS_DIR, fontFile.replace(/\.(ttf|otf)$/, '-subset.$1'))
  
  if (!fs.existsSync(fontPath)) {
    console.warn(`⚠️  Font file not found: ${fontFile}`)
    return null
  }

  return {
    input: fontPath,
    output: outputPath,
    subset: config.subset,
    commonChars: config.commonChars,
    priority: config.priority,
    name: config.name
  }
}

/**
 * Generate optimized font-face CSS with font-display: swap and subsets
 */
function generateOptimizedFontCSS() {
  let css = `/* Optimized Font Face Declarations with Subsets and Lazy Loading */\n\n`
  
  // Critical fonts (loaded immediately)
  css += `/* Critical Fonts - Loaded Immediately */\n`
  css += `@font-face {\n`
  css += `  font-family: "MatrixBoldSmallCaps";\n`
  css += `  src: url("MatrixBoldSmallCaps.ttf") format("truetype");\n`
  css += `  font-weight: bold;\n`
  css += `  font-display: swap;\n`
  css += `  font-loading: critical;\n`
  css += `}\n\n`

  // High priority fonts (preloaded)
  css += `/* High Priority Fonts - Preloaded */\n`
  Object.entries(FONT_CONFIG).forEach(([fontFile, config]) => {
    if (config.priority === 'high') {
      const fontFamily = fontFile.replace(/\.(ttf|otf)$/, '')
      const subsetFile = fontFile.replace(/\.(ttf|otf)$/, '-subset.$1')
      
      css += `@font-face {\n`
      css += `  font-family: "${fontFamily}";\n`
      css += `  src: url("optimized/${subsetFile}") format("truetype");\n`
      css += `  font-display: swap;\n`
      css += `  font-loading: high-priority;\n`
      css += `  unicode-range: U+0020-007F; /* Basic Latin */\n`
      css += `}\n\n`
    }
  })

  // Medium priority fonts (lazy loaded)
  css += `/* Medium Priority Fonts - Lazy Loaded */\n`
  Object.entries(FONT_CONFIG).forEach(([fontFile, config]) => {
    if (config.priority === 'medium') {
      const fontFamily = fontFile.replace(/\.(ttf|otf)$/, '')
      
      css += `@font-face {\n`
      css += `  font-family: "${fontFamily}";\n`
      css += `  src: url("${fontFile}") format("truetype");\n`
      css += `  font-display: swap;\n`
      css += `  font-loading: lazy;\n`
      css += `}\n\n`
    }
  })

  // Low priority fonts (loaded on demand)
  css += `/* Low Priority Fonts - Loaded on Demand */\n`
  Object.entries(FONT_CONFIG).forEach(([fontFile, config]) => {
    if (config.priority === 'low') {
      const fontFamily = fontFile.replace(/\.(ttf|otf)$/, '')
      
      css += `@font-face {\n`
      css += `  font-family: "${fontFamily}";\n`
      css += `  src: url("${fontFile}") format("truetype");\n`
      css += `  font-display: optional;\n`
      css += `  font-loading: on-demand;\n`
      css += `}\n\n`
    }
  })

  // Link font
  css += `/* Special Fonts */\n`
  css += `@font-face {\n`
  css += `  font-family: "link";\n`
  css += `  src: url("link.ttf") format("truetype");\n`
  css += `  font-display: swap;\n`
  css += `  font-loading: lazy;\n`
  css += `}\n`

  return css
}

/**
 * Generate font loading JavaScript for progressive enhancement
 */
function generateFontLoadingJS() {
  return `
/**
 * Progressive Font Loading for Yu-Gi-Oh Card Maker
 * Implements smart font loading based on user language and usage patterns
 */

class FontLoader {
  constructor() {
    this.loadedFonts = new Set()
    this.currentLanguage = 'en'
    this.fontLoadPromises = new Map()
  }

  /**
   * Initialize font loading based on current language
   */
  async init(language = 'en') {
    this.currentLanguage = language
    
    // Always load critical fonts first
    await this.loadCriticalFonts()
    
    // Load language-specific fonts
    await this.loadLanguageFonts(language)
    
    // Preload likely next fonts
    this.preloadLikelyFonts(language)
  }

  /**
   * Load critical fonts immediately
   */
  async loadCriticalFonts() {
    const criticalFonts = ['MatrixBoldSmallCaps', 'en']
    
    for (const fontFamily of criticalFonts) {
      await this.loadFont(fontFamily, 'critical')
    }
  }

  /**
   * Load fonts for specific language
   */
  async loadLanguageFonts(language) {
    const languageFontMap = {
      'zh': ['zh', 'cn'],
      'ja': ['jp', 'jp2'], 
      'en': ['en', 'en2'],
      'ko': ['jp'], // Korean can use Japanese fonts as fallback
      'de': ['en', 'en2'],
      'fr': ['en', 'en2'],
      'pt': ['en', 'en2'],
      'es': ['en', 'en2'],
      'el': ['en', 'en2'],
      'th': ['en', 'en2'],
      'ru': ['en', 'en2'],
      'vi': ['en', 'en2']
    }

    const fontsToLoad = languageFontMap[language] || ['en']
    
    for (const fontFamily of fontsToLoad) {
      await this.loadFont(fontFamily, 'high-priority')
    }
  }

  /**
   * Load a specific font with priority
   */
  async loadFont(fontFamily, priority = 'normal') {
    if (this.loadedFonts.has(fontFamily)) {
      return Promise.resolve()
    }

    if (this.fontLoadPromises.has(fontFamily)) {
      return this.fontLoadPromises.get(fontFamily)
    }

    const loadPromise = new Promise((resolve, reject) => {
      if (!document.fonts) {
        // Fallback for browsers without Font Loading API
        resolve()
        return
      }

      const font = new FontFace(fontFamily, \`url('/fonts/\${fontFamily}.ttf')\`)
      
      font.load().then(() => {
        document.fonts.add(font)
        this.loadedFonts.add(fontFamily)
        console.log(\`✅ Font loaded: \${fontFamily}\`)
        resolve()
      }).catch((error) => {
        console.warn(\`⚠️  Failed to load font: \${fontFamily}\`, error)
        resolve() // Don't reject to avoid breaking the app
      })
    })

    this.fontLoadPromises.set(fontFamily, loadPromise)
    return loadPromise
  }

  /**
   * Preload fonts that are likely to be needed
   */
  preloadLikelyFonts(currentLanguage) {
    const likelyLanguages = this.getLikelyNextLanguages(currentLanguage)
    
    // Use requestIdleCallback for non-critical preloading
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        likelyLanguages.forEach(lang => {
          this.loadLanguageFonts(lang)
        })
      })
    }
  }

  /**
   * Get likely next languages based on current language
   */
  getLikelyNextLanguages(currentLanguage) {
    const languageGroups = {
      'en': ['zh', 'ja'],
      'zh': ['en', 'ja'],
      'ja': ['en', 'zh'],
      'ko': ['ja', 'zh'],
      'de': ['en', 'fr'],
      'fr': ['en', 'de'],
      'pt': ['es', 'en'],
      'es': ['pt', 'en'],
      'el': ['en'],
      'th': ['en'],
      'ru': ['en'],
      'vi': ['en']
    }

    return languageGroups[currentLanguage] || ['en']
  }

  /**
   * Check if font is loaded
   */
  isFontLoaded(fontFamily) {
    return this.loadedFonts.has(fontFamily)
  }

  /**
   * Get loading status
   */
  getLoadingStatus() {
    return {
      loadedFonts: Array.from(this.loadedFonts),
      currentLanguage: this.currentLanguage,
      totalLoaded: this.loadedFonts.size
    }
  }
}

// Global font loader instance
window.fontLoader = new FontLoader()

// Auto-initialize on DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.fontLoader.init()
  })
} else {
  window.fontLoader.init()
}
`
}

/**
 * Main optimization function
 */
async function optimizeFonts() {
  try {
    createOptimizedFontsDir()
    
    // Generate optimized font CSS
    const optimizedCSS = generateOptimizedFontCSS()
    const cssPath = path.join(FONTS_DIR, 'font-face-optimized.css')
    fs.writeFileSync(cssPath, optimizedCSS)
    console.log('✅ Generated optimized font CSS')
    
    // Generate font loading JavaScript
    const fontLoadingJS = generateFontLoadingJS()
    const jsPath = path.join(STATIC_DIR, 'js', 'font-loader.js')
    
    // Create js directory if it doesn't exist
    const jsDir = path.dirname(jsPath)
    if (!fs.existsSync(jsDir)) {
      fs.mkdirSync(jsDir, { recursive: true })
    }
    
    fs.writeFileSync(jsPath, fontLoadingJS)
    console.log('✅ Generated font loading JavaScript')
    
    // Log font analysis
    console.log('\n📊 Font Analysis:')
    Object.entries(FONT_CONFIG).forEach(([fontFile, config]) => {
      const fontPath = path.join(FONTS_DIR, fontFile)
      if (fs.existsSync(fontPath)) {
        const stats = fs.statSync(fontPath)
        const sizeMB = (stats.size / 1024 / 1024).toFixed(2)
        console.log(`  ${fontFile}: ${sizeMB}MB (${config.priority} priority)`)
      }
    })
    
    console.log('\n🎉 Font optimization completed!')
    console.log('\nNext steps:')
    console.log('1. Update nuxt.config.js to use optimized font CSS')
    console.log('2. Add font-loader.js to the page head')
    console.log('3. Consider using a font subsetting tool like pyftsubset for further optimization')
    
  } catch (error) {
    console.error('❌ Font optimization failed:', error.message)
    process.exit(1)
  }
}

// Run optimization
optimizeFonts()
