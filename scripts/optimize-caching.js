#!/usr/bin/env node

/**
 * Caching Strategy Optimization Script for Yu-Gi-Oh Card Maker
 * This script implements advanced caching strategies and Service Worker
 */

const fs = require('fs')
const path = require('path')

const STATIC_DIR = path.join(__dirname, '..', 'static')
const SW_DIR = path.join(STATIC_DIR, 'sw')

console.log('💾 Starting caching strategy optimization...')

/**
 * Caching configuration
 */
const CACHE_CONFIG = {
  // Static assets with long-term caching
  staticAssets: {
    cacheName: 'yugioh-static-v1',
    strategy: 'CacheFirst',
    maxAge: 365 * 24 * 60 * 60, // 1 year
    resources: [
      '/fonts/',
      '/images/card/',
      '/images/attr/',
      '/css/',
      '/js/'
    ]
  },
  // API responses with network-first strategy
  apiResponses: {
    cacheName: 'yugioh-api-v1',
    strategy: 'NetworkFirst',
    maxAge: 24 * 60 * 60, // 1 day
    resources: [
      '/api/',
      '/_nuxt/data/'
    ]
  },
  // HTML pages with stale-while-revalidate
  htmlPages: {
    cacheName: 'yugioh-pages-v1',
    strategy: 'StaleWhileRevalidate',
    maxAge: 7 * 24 * 60 * 60, // 1 week
    resources: [
      '/',
      '/about',
      '/privacy',
      '/terms'
    ]
  }
}

/**
 * Create service worker directory
 */
function createServiceWorkerDir() {
  if (!fs.existsSync(SW_DIR)) {
    fs.mkdirSync(SW_DIR, { recursive: true })
  }
  console.log('✅ Created service worker directory')
}

/**
 * Generate Service Worker
 */
function generateServiceWorker() {
  return `
/**
 * Service Worker for Yu-Gi-Oh Card Maker
 * Implements advanced caching strategies for optimal performance
 */

const CACHE_VERSION = 'v1.0.0'
const CACHE_NAMES = {
  static: 'yugioh-static-v1',
  api: 'yugioh-api-v1',
  pages: 'yugioh-pages-v1',
  fonts: 'yugioh-fonts-v1',
  images: 'yugioh-images-v1'
}

const STATIC_CACHE_URLS = [
  '/',
  '/css/critical/critical.css',
  '/js/font-loader.js',
  '/js/image-loader.js',
  '/js/css-loader.js',
  '/fonts/MatrixBoldSmallCaps.ttf',
  '/fonts/en.ttf',
  '/images/logo.png'
]

/**
 * Install event - Cache critical resources
 */
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(CACHE_NAMES.static)
      .then((cache) => {
        console.log('Caching critical resources...')
        return cache.addAll(STATIC_CACHE_URLS)
      })
      .then(() => {
        console.log('Critical resources cached successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Failed to cache critical resources:', error)
      })
  )
})

/**
 * Activate event - Clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        const deletePromises = cacheNames
          .filter((cacheName) => {
            return !Object.values(CACHE_NAMES).includes(cacheName)
          })
          .map((cacheName) => {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          })
        
        return Promise.all(deletePromises)
      })
      .then(() => {
        console.log('Old caches cleaned up')
        return self.clients.claim()
      })
  )
})

/**
 * Fetch event - Implement caching strategies
 */
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return
  }
  
  event.respondWith(handleRequest(request))
})

/**
 * Handle different types of requests with appropriate caching strategies
 */
async function handleRequest(request) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  try {
    // Font files - Cache First with long expiration
    if (pathname.startsWith('/fonts/')) {
      return await cacheFirst(request, CACHE_NAMES.fonts, 365 * 24 * 60 * 60)
    }
    
    // Card template images - Cache First with long expiration
    if (pathname.startsWith('/images/card/') || pathname.startsWith('/images/attr/')) {
      return await cacheFirst(request, CACHE_NAMES.images, 30 * 24 * 60 * 60)
    }
    
    // Static assets (CSS, JS) - Cache First with versioning
    if (pathname.startsWith('/css/') || pathname.startsWith('/js/') || pathname.startsWith('/_nuxt/')) {
      return await cacheFirst(request, CACHE_NAMES.static, 7 * 24 * 60 * 60)
    }
    
    // API requests - Network First with fallback
    if (pathname.startsWith('/api/') || pathname.startsWith('/_nuxt/data/')) {
      return await networkFirst(request, CACHE_NAMES.api, 24 * 60 * 60)
    }
    
    // HTML pages - Stale While Revalidate
    if (pathname === '/' || pathname.endsWith('.html') || !pathname.includes('.')) {
      return await staleWhileRevalidate(request, CACHE_NAMES.pages, 7 * 24 * 60 * 60)
    }
    
    // Default - Network with cache fallback
    return await networkWithCacheFallback(request)
    
  } catch (error) {
    console.error('Request handling failed:', error)
    return await networkWithCacheFallback(request)
  }
}

/**
 * Cache First strategy - Good for static assets
 */
async function cacheFirst(request, cacheName, maxAge) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    // Check if cache is still fresh
    const cacheDate = new Date(cachedResponse.headers.get('date') || Date.now())
    const now = new Date()
    const age = (now - cacheDate) / 1000
    
    if (age < maxAge) {
      return cachedResponse
    }
  }
  
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse
    }
    throw error
  }
}

/**
 * Network First strategy - Good for API responses
 */
async function networkFirst(request, cacheName, maxAge) {
  const cache = await caches.open(cacheName)
  
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    const cachedResponse = await cache.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    throw error
  }
}

/**
 * Stale While Revalidate strategy - Good for HTML pages
 */
async function staleWhileRevalidate(request, cacheName, maxAge) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  // Always try to fetch from network in background
  const networkPromise = fetch(request)
    .then((networkResponse) => {
      if (networkResponse.ok) {
        cache.put(request, networkResponse.clone())
      }
      return networkResponse
    })
    .catch(() => {
      // Network failed, but we might have cache
    })
  
  // Return cached response immediately if available
  if (cachedResponse) {
    // Update cache in background
    networkPromise.catch(() => {})
    return cachedResponse
  }
  
  // No cache, wait for network
  return await networkPromise
}

/**
 * Network with cache fallback - Default strategy
 */
async function networkWithCacheFallback(request) {
  try {
    return await fetch(request)
  } catch (error) {
    const cache = await caches.open(CACHE_NAMES.static)
    const cachedResponse = await cache.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    throw error
  }
}

/**
 * Background sync for offline actions
 */
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

async function doBackgroundSync() {
  // Handle offline actions when connection is restored
  console.log('Background sync triggered')
}

/**
 * Push notification handling
 */
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json()
    const options = {
      body: data.body,
      icon: '/images/logo.png',
      badge: '/images/badge.png',
      data: data.data
    }
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    )
  }
})

/**
 * Notification click handling
 */
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  
  event.waitUntil(
    clients.openWindow(event.notification.data.url || '/')
  )
})
`
}

/**
 * Generate cache configuration for Nuxt
 */
function generateNuxtCacheConfig() {
  return `
// Cache configuration for Nuxt.js
export default {
  // Static asset caching
  static: {
    maxAge: 1000 * 60 * 60 * 24 * 365, // 1 year
    setHeaders(res, path) {
      if (path.includes('/fonts/')) {
        res.setHeader('Cache-Control', 'public, max-age=31536000, immutable')
      } else if (path.includes('/images/card/') || path.includes('/images/attr/')) {
        res.setHeader('Cache-Control', 'public, max-age=2592000') // 30 days
      } else if (path.includes('/css/') || path.includes('/js/')) {
        res.setHeader('Cache-Control', 'public, max-age=604800') // 7 days
      }
    }
  },
  
  // PWA configuration
  pwa: {
    workbox: {
      enabled: true,
      swDest: 'sw.js',
      runtimeCaching: [
        {
          urlPattern: /^https:\\/\\/fonts\\.(googleapis|gstatic)\\.com/,
          handler: 'CacheFirst',
          options: {
            cacheName: 'google-fonts',
            expiration: {
              maxEntries: 30,
              maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
            }
          }
        },
        {
          urlPattern: /\\/_nuxt\\//,
          handler: 'CacheFirst',
          options: {
            cacheName: 'nuxt-assets',
            expiration: {
              maxEntries: 100,
              maxAgeSeconds: 60 * 60 * 24 * 7 // 1 week
            }
          }
        }
      ]
    }
  }
}
`
}

/**
 * Main optimization function
 */
async function optimizeCaching() {
  try {
    createServiceWorkerDir()
    
    // Generate Service Worker
    const serviceWorker = generateServiceWorker()
    const swPath = path.join(STATIC_DIR, 'sw.js')
    fs.writeFileSync(swPath, serviceWorker)
    console.log('✅ Generated Service Worker')
    
    // Generate Nuxt cache configuration
    const nuxtCacheConfig = generateNuxtCacheConfig()
    const configPath = path.join(__dirname, '..', 'cache.config.js')
    fs.writeFileSync(configPath, nuxtCacheConfig)
    console.log('✅ Generated Nuxt cache configuration')
    
    console.log('\n🎉 Caching strategy optimization completed!')
    console.log('\nNext steps:')
    console.log('1. Register Service Worker in the main application')
    console.log('2. Update server configuration for static asset caching')
    console.log('3. Implement cache invalidation strategies')
    console.log('4. Test caching behavior in different scenarios')
    console.log('5. Monitor cache hit rates and performance improvements')
    
  } catch (error) {
    console.error('❌ Caching optimization failed:', error.message)
    process.exit(1)
  }
}

// Run optimization
optimizeCaching()
