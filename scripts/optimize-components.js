#!/usr/bin/env node

/**
 * Component Optimization Script for Yu-Gi-Oh Card Maker
 * This script analyzes and optimizes large Vue components for better performance
 */

const fs = require('fs')
const path = require('path')

const PAGES_DIR = path.join(__dirname, '..', 'pages')
const COMPONENTS_DIR = path.join(__dirname, '..', 'components')
const OPTIMIZED_COMPONENTS_DIR = path.join(COMPONENTS_DIR, 'optimized')

console.log('⚡ Starting component optimization...')

/**
 * Component splitting configuration
 */
const COMPONENT_SPLITS = {
  'CardEditor': {
    sections: [
      'CardPreview',
      'CardForm', 
      'CardSettings',
      'CardActions'
    ],
    priority: 'high',
    lazyLoad: false
  },
  'ContentSections': {
    sections: [
      'FeaturesSection',
      'HowToUseSection', 
      'FAQSection'
    ],
    priority: 'low',
    lazyLoad: true
  },
  'Navigation': {
    sections: [
      'MainNavbar',
      'LanguageSwitcher'
    ],
    priority: 'high',
    lazyLoad: false
  }
}

/**
 * Create optimized components directory
 */
function createOptimizedComponentsDir() {
  if (!fs.existsSync(OPTIMIZED_COMPONENTS_DIR)) {
    fs.mkdirSync(OPTIMIZED_COMPONENTS_DIR, { recursive: true })
    console.log('✅ Created optimized components directory')
  }
}

/**
 * Analyze current component structure
 */
function analyzeComponents() {
  console.log('\n📊 Component Analysis:')
  
  const indexPath = path.join(PAGES_DIR, 'index.vue')
  if (fs.existsSync(indexPath)) {
    const content = fs.readFileSync(indexPath, 'utf8')
    const lines = content.split('\n').length
    const sizeKB = (Buffer.byteLength(content, 'utf8') / 1024).toFixed(1)
    
    console.log(`  index.vue: ${lines} lines, ${sizeKB}KB`)
    
    // Analyze template sections
    const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/)
    if (templateMatch) {
      const templateLines = templateMatch[1].split('\n').length
      console.log(`    Template: ${templateLines} lines`)
    }
    
    // Analyze script sections
    const scriptMatch = content.match(/<script>([\s\S]*?)<\/script>/)
    if (scriptMatch) {
      const scriptLines = scriptMatch[1].split('\n').length
      console.log(`    Script: ${scriptLines} lines`)
    }
  }
  
  // Analyze existing components
  if (fs.existsSync(COMPONENTS_DIR)) {
    const components = fs.readdirSync(COMPONENTS_DIR).filter(file => file.endsWith('.vue'))
    console.log(`\n  Existing components: ${components.length}`)
    components.forEach(comp => {
      const compPath = path.join(COMPONENTS_DIR, comp)
      const content = fs.readFileSync(compPath, 'utf8')
      const lines = content.split('\n').length
      const sizeKB = (Buffer.byteLength(content, 'utf8') / 1024).toFixed(1)
      console.log(`    ${comp}: ${lines} lines, ${sizeKB}KB`)
    })
  }
}

/**
 * Generate Card Preview Component
 */
function generateCardPreviewComponent() {
  return `<template>
  <aside class="col-lg-5 col-xl-6 preview-panel" role="complementary">
    <div class="preview-container">
      <div class="card-preview-wrapper">
        <div
          id="yugiohcard-wrap"
          ref="yugiohcard-wrap"
          class="card-display"
          @mousemove="move"
          @mouseleave="leave"
          role="img"
          :aria-label="cardTitle || 'Yu-Gi-Oh! Card Preview'"
        >
          <canvas
            id="yugiohcard"
            ref="yugiohcard"
            class="card-canvas"
            :aria-label="\`\${cardTitle || 'Untitled Card'} - \${cardType} Card\`"
          ></canvas>
        </div>
        
        <!-- Preview Controls -->
        <div class="preview-controls mt-3">
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary me-2"
            @click="resetCardRotation"
            :aria-label="$t('controls.resetRotation')"
          >
            <fa :icon="['fas', 'undo']" />
          </button>
          
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary me-2"
            @click="toggleFullscreen"
            :aria-label="$t('controls.fullscreen')"
          >
            <fa :icon="['fas', 'expand']" />
          </button>
          
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary"
            @click="copyCardImage"
            :aria-label="$t('controls.copy')"
          >
            <fa :icon="['fas', 'copy']" />
          </button>
        </div>
      </div>
    </div>
  </aside>
</template>

<script>
export default {
  name: 'CardPreview',
  
  props: {
    cardTitle: {
      type: String,
      default: ''
    },
    cardType: {
      type: String,
      default: 'Monster'
    }
  },
  
  methods: {
    move(event) {
      this.$emit('card-move', event)
    },
    
    leave(event) {
      this.$emit('card-leave', event)
    },
    
    resetCardRotation() {
      this.$emit('reset-rotation')
    },
    
    toggleFullscreen() {
      this.$emit('toggle-fullscreen')
    },
    
    copyCardImage() {
      this.$emit('copy-image')
    }
  }
}
</script>

<style scoped>
.preview-panel {
  position: sticky;
  top: 80px;
  height: fit-content;
}

.card-display {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.1s ease-out;
}

.card-canvas {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.preview-controls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

@media (max-width: 991px) {
  .preview-panel {
    position: static;
    margin-bottom: 2rem;
  }
}
</style>`
}

/**
 * Generate Card Form Component
 */
function generateCardFormComponent() {
  return `<template>
  <section class="col-lg-7 col-xl-6 editor-panel" role="form">
    <div class="editor-container">
      <div class="editor-content">
        <form @submit.prevent="onSubmit" class="card-settings-form">
          
          <!-- Basic Settings -->
          <CardBasicSettings
            v-model="basicSettings"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onBasicSettingsUpdate"
          />
          
          <!-- Card Information -->
          <CardInformation
            v-model="cardInfo"
            :card-type="cardType"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onCardInfoUpdate"
          />
          
          <!-- Monster Settings -->
          <CardMonsterSettings
            v-if="cardType === 'Monster'"
            v-model="monsterSettings"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onMonsterSettingsUpdate"
          />
          
          <!-- Pendulum Settings -->
          <CardPendulumSettings
            v-if="showPendulum"
            v-model="pendulumSettings"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onPendulumSettingsUpdate"
          />
          
          <!-- Card Actions -->
          <CardActions
            :ui-lang="uiLang"
            :ui="ui"
            @generate="onGenerate"
            @download="onDownload"
            @reset="onReset"
          />
          
        </form>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'CardForm',
  
  components: {
    CardBasicSettings: () => import('./CardBasicSettings.vue'),
    CardInformation: () => import('./CardInformation.vue'),
    CardMonsterSettings: () => import('./CardMonsterSettings.vue'),
    CardPendulumSettings: () => import('./CardPendulumSettings.vue'),
    CardActions: () => import('./CardActions.vue')
  },
  
  props: {
    uiLang: {
      type: String,
      default: 'en'
    },
    ui: {
      type: Object,
      default: () => ({})
    },
    cardType: {
      type: String,
      default: 'Monster'
    }
  },
  
  data() {
    return {
      basicSettings: {},
      cardInfo: {},
      monsterSettings: {},
      pendulumSettings: {}
    }
  },
  
  computed: {
    showPendulum() {
      return this.cardType === 'Monster' && this.monsterSettings.pendulum
    }
  },
  
  methods: {
    onSubmit() {
      this.onGenerate()
    },
    
    onBasicSettingsUpdate(settings) {
      this.basicSettings = { ...settings }
      this.$emit('update:basic-settings', this.basicSettings)
    },
    
    onCardInfoUpdate(info) {
      this.cardInfo = { ...info }
      this.$emit('update:card-info', this.cardInfo)
    },
    
    onMonsterSettingsUpdate(settings) {
      this.monsterSettings = { ...settings }
      this.$emit('update:monster-settings', this.monsterSettings)
    },
    
    onPendulumSettingsUpdate(settings) {
      this.pendulumSettings = { ...settings }
      this.$emit('update:pendulum-settings', this.pendulumSettings)
    },
    
    onGenerate() {
      this.$emit('generate-card')
    },
    
    onDownload() {
      this.$emit('download-card')
    },
    
    onReset() {
      this.$emit('reset-card')
    }
  }
}
</script>

<style scoped>
.editor-panel {
  padding: 1rem;
}

.card-settings-form {
  max-width: 100%;
}

@media (max-width: 991px) {
  .editor-panel {
    padding: 0.5rem;
  }
}
</style>`
}

/**
 * Generate lazy-loaded content sections component
 */
function generateContentSectionsComponent() {
  return `<template>
  <div class="content-sections">
    <!-- Features Section -->
    <section 
      id="features" 
      class="features-section py-5 anchor-section" 
      role="region"
      v-intersection="onFeaturesVisible"
    >
      <FeaturesSection 
        v-if="featuresLoaded"
        :features-data="featuresData"
      />
      <div v-else class="section-placeholder">
        <div class="loading-spinner"></div>
      </div>
    </section>

    <!-- How to Use Section -->
    <section 
      id="how-to-use" 
      class="how-to-use-section py-5 anchor-section" 
      role="region"
      v-intersection="onHowToUseVisible"
    >
      <HowToUseSection 
        v-if="howToUseLoaded"
        :steps-data="stepsData"
      />
      <div v-else class="section-placeholder">
        <div class="loading-spinner"></div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section 
      id="faq" 
      class="faq-section py-5 anchor-section" 
      role="region"
      v-intersection="onFaqVisible"
    >
      <FAQSection 
        v-if="faqLoaded"
        :faq-data="faqData"
      />
      <div v-else class="section-placeholder">
        <div class="loading-spinner"></div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'ContentSections',
  
  components: {
    FeaturesSection: () => import('~/components/FeaturesSection.vue'),
    HowToUseSection: () => import('~/components/HowToUseSection.vue'),
    FAQSection: () => import('~/components/FAQSection.vue')
  },
  
  directives: {
    intersection: {
      inserted(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value()
              observer.unobserve(el)
            }
          })
        }, {
          rootMargin: '100px 0px',
          threshold: 0.1
        })
        
        observer.observe(el)
      }
    }
  },
  
  props: {
    featuresData: {
      type: Array,
      default: () => []
    },
    stepsData: {
      type: Array,
      default: () => []
    },
    faqData: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      featuresLoaded: false,
      howToUseLoaded: false,
      faqLoaded: false
    }
  },
  
  methods: {
    onFeaturesVisible() {
      if (!this.featuresLoaded) {
        this.featuresLoaded = true
      }
    },
    
    onHowToUseVisible() {
      if (!this.howToUseLoaded) {
        this.howToUseLoaded = true
      }
    },
    
    onFaqVisible() {
      if (!this.faqLoaded) {
        this.faqLoaded = true
      }
    }
  }
}
</script>

<style scoped>
.section-placeholder {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>`
}

/**
 * Main optimization function
 */
async function optimizeComponents() {
  try {
    createOptimizedComponentsDir()
    analyzeComponents()
    
    // Generate optimized components
    const cardPreview = generateCardPreviewComponent()
    fs.writeFileSync(path.join(OPTIMIZED_COMPONENTS_DIR, 'CardPreview.vue'), cardPreview)
    console.log('✅ Generated CardPreview component')
    
    const cardForm = generateCardFormComponent()
    fs.writeFileSync(path.join(OPTIMIZED_COMPONENTS_DIR, 'CardForm.vue'), cardForm)
    console.log('✅ Generated CardForm component')
    
    const contentSections = generateContentSectionsComponent()
    fs.writeFileSync(path.join(OPTIMIZED_COMPONENTS_DIR, 'ContentSections.vue'), contentSections)
    console.log('✅ Generated ContentSections component')
    
    console.log('\n🎉 Component optimization completed!')
    console.log('\nNext steps:')
    console.log('1. Refactor pages/index.vue to use the new components')
    console.log('2. Implement proper prop passing and event handling')
    console.log('3. Add error boundaries for lazy-loaded components')
    console.log('4. Test component loading and functionality')
    console.log('5. Monitor bundle size reduction')
    
  } catch (error) {
    console.error('❌ Component optimization failed:', error.message)
    process.exit(1)
  }
}

// Run optimization
optimizeComponents()
