#!/usr/bin/env node

/**
 * Image Optimization Script for Yu-Gi-Oh Card Maker
 * This script optimizes card template images and other static assets
 */

const fs = require('fs')
const path = require('path')

const STATIC_DIR = path.join(__dirname, '..', 'static')
const IMAGES_DIR = path.join(STATIC_DIR, 'images')
const OPTIMIZED_DIR = path.join(IMAGES_DIR, 'optimized')

console.log('🖼️  Starting image optimization...')

/**
 * Image optimization configuration
 */
const IMAGE_CONFIG = {
  cardTemplates: {
    path: 'card',
    formats: ['webp', 'jpg'],
    quality: 85,
    priority: 'high',
    lazyLoad: false // Card templates are critical
  },
  attributes: {
    path: 'attr',
    formats: ['webp'],
    quality: 90,
    priority: 'medium',
    lazyLoad: true
  },
  icons: {
    path: 'pic',
    formats: ['webp', 'png'],
    quality: 95,
    priority: 'low',
    lazyLoad: true
  },
  backgrounds: {
    path: '',
    formats: ['webp', 'jpg'],
    quality: 80,
    priority: 'medium',
    lazyLoad: false
  }
}

/**
 * Create optimized images directory structure
 */
function createOptimizedDirs() {
  if (!fs.existsSync(OPTIMIZED_DIR)) {
    fs.mkdirSync(OPTIMIZED_DIR, { recursive: true })
  }
  
  Object.values(IMAGE_CONFIG).forEach(config => {
    if (config.path) {
      const dir = path.join(OPTIMIZED_DIR, config.path)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
    }
  })
  
  console.log('✅ Created optimized images directory structure')
}

/**
 * Analyze current image sizes
 */
function analyzeImages() {
  console.log('\n📊 Image Analysis:')
  
  const cardDir = path.join(IMAGES_DIR, 'card')
  if (fs.existsSync(cardDir)) {
    const languages = fs.readdirSync(cardDir).filter(item => 
      fs.statSync(path.join(cardDir, item)).isDirectory()
    )
    
    let totalSize = 0
    let fileCount = 0
    
    languages.forEach(lang => {
      const langDir = path.join(cardDir, lang)
      const files = fs.readdirSync(langDir).filter(file => 
        file.endsWith('.png') || file.endsWith('.jpg')
      )
      
      console.log(`\n  ${lang.toUpperCase()} Card Templates:`)
      files.forEach(file => {
        const filePath = path.join(langDir, file)
        const stats = fs.statSync(filePath)
        const sizeKB = (stats.size / 1024).toFixed(1)
        totalSize += stats.size
        fileCount++
        console.log(`    ${file}: ${sizeKB}KB`)
      })
    })
    
    console.log(`\n  Total: ${fileCount} files, ${(totalSize / 1024 / 1024).toFixed(2)}MB`)
  }
}

/**
 * Generate responsive image configuration
 */
function generateResponsiveImageConfig() {
  const config = {
    cardTemplates: {
      sizes: [
        { width: 400, suffix: '-sm', quality: 80 },
        { width: 600, suffix: '-md', quality: 85 },
        { width: 1000, suffix: '', quality: 90 }
      ],
      formats: ['webp', 'jpg']
    },
    attributes: {
      sizes: [
        { width: 45, suffix: '-sm', quality: 90 },
        { width: 90, suffix: '', quality: 95 }
      ],
      formats: ['webp', 'png']
    }
  }
  
  return config
}

/**
 * Generate lazy loading JavaScript
 */
function generateLazyLoadingJS() {
  return `
/**
 * Lazy Loading for Images - Yu-Gi-Oh Card Maker
 * Implements progressive image loading with WebP support
 */

class ImageLoader {
  constructor() {
    this.observer = null
    this.supportsWebP = false
    this.loadedImages = new Set()
    this.init()
  }

  /**
   * Initialize the image loader
   */
  async init() {
    // Check WebP support
    this.supportsWebP = await this.checkWebPSupport()
    
    // Initialize Intersection Observer for lazy loading
    this.initIntersectionObserver()
    
    // Load critical images immediately
    this.loadCriticalImages()
    
    // Observe all lazy images
    this.observeLazyImages()
  }

  /**
   * Check if browser supports WebP
   */
  checkWebPSupport() {
    return new Promise((resolve) => {
      const webP = new Image()
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2)
      }
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
    })
  }

  /**
   * Initialize Intersection Observer
   */
  initIntersectionObserver() {
    if (!('IntersectionObserver' in window)) {
      // Fallback for older browsers
      this.loadAllImages()
      return
    }

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadImage(entry.target)
          this.observer.unobserve(entry.target)
        }
      })
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    })
  }

  /**
   * Load critical images immediately
   */
  loadCriticalImages() {
    const criticalImages = document.querySelectorAll('img[data-critical="true"]')
    criticalImages.forEach(img => this.loadImage(img))
  }

  /**
   * Observe lazy images
   */
  observeLazyImages() {
    const lazyImages = document.querySelectorAll('img[data-lazy="true"]')
    lazyImages.forEach(img => {
      if (this.observer) {
        this.observer.observe(img)
      } else {
        this.loadImage(img)
      }
    })
  }

  /**
   * Load a specific image
   */
  loadImage(img) {
    if (this.loadedImages.has(img)) return

    const src = this.getOptimalImageSrc(img)
    
    // Create a new image to preload
    const imageLoader = new Image()
    
    imageLoader.onload = () => {
      img.src = src
      img.classList.add('loaded')
      this.loadedImages.add(img)
    }
    
    imageLoader.onerror = () => {
      // Fallback to original src
      const fallbackSrc = img.dataset.fallback || img.dataset.src
      if (fallbackSrc) {
        img.src = fallbackSrc
        img.classList.add('loaded')
      }
    }
    
    imageLoader.src = src
  }

  /**
   * Get optimal image source based on device and format support
   */
  getOptimalImageSrc(img) {
    const baseSrc = img.dataset.src
    const webpSrc = img.dataset.webp
    
    // Use WebP if supported and available
    if (this.supportsWebP && webpSrc) {
      return webpSrc
    }
    
    // Get responsive size
    const width = img.offsetWidth || img.dataset.width || 400
    const responsiveSrc = this.getResponsiveSrc(baseSrc, width)
    
    return responsiveSrc || baseSrc
  }

  /**
   * Get responsive image source
   */
  getResponsiveSrc(baseSrc, width) {
    if (!baseSrc) return null
    
    const ext = baseSrc.split('.').pop()
    const base = baseSrc.replace(new RegExp('\\.' + ext + '$'), '')
    
    if (width <= 400) {
      return \`\${base}-sm.\${ext}\`
    } else if (width <= 600) {
      return \`\${base}-md.\${ext}\`
    }
    
    return baseSrc
  }

  /**
   * Load all images (fallback)
   */
  loadAllImages() {
    const allImages = document.querySelectorAll('img[data-src]')
    allImages.forEach(img => this.loadImage(img))
  }

  /**
   * Preload images for next language
   */
  preloadLanguageImages(language) {
    const imagesToPreload = [
      \`/images/card/\${language}/Normal.png\`,
      \`/images/card/\${language}/Effect.png\`,
      \`/images/card/\${language}/Spell.png\`,
      \`/images/card/\${language}/Trap.png\`
    ]
    
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        imagesToPreload.forEach(src => {
          const img = new Image()
          img.src = src
        })
      })
    }
  }
}

// Global image loader instance
window.imageLoader = new ImageLoader()

// Auto-initialize on DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.imageLoader.init()
  })
} else {
  window.imageLoader.init()
}
`
}

/**
 * Generate optimized CSS for images
 */
function generateImageCSS() {
  return `
/* Optimized Image Styles for Yu-Gi-Oh Card Maker */

/* Base image styles */
img {
  max-width: 100%;
  height: auto;
}

/* Lazy loading states */
img[data-lazy="true"] {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

img[data-lazy="true"].loaded {
  opacity: 1;
}

/* Card template images */
.card-template {
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* Responsive card images */
@media (max-width: 576px) {
  .card-template {
    max-width: 400px;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .card-template {
    max-width: 600px;
  }
}

@media (min-width: 769px) {
  .card-template {
    max-width: 1000px;
  }
}

/* Attribute icons */
.attr-icon {
  width: 90px;
  height: 90px;
  object-fit: contain;
}

@media (max-width: 576px) {
  .attr-icon {
    width: 45px;
    height: 45px;
  }
}

/* Loading placeholder */
.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* WebP support detection */
.webp .card-template[data-webp] {
  content: attr(data-webp);
}

.no-webp .card-template[data-fallback] {
  content: attr(data-fallback);
}
`
}

/**
 * Main optimization function
 */
async function optimizeImages() {
  try {
    createOptimizedDirs()
    analyzeImages()
    
    // Generate lazy loading JavaScript
    const lazyLoadJS = generateLazyLoadingJS()
    const jsPath = path.join(STATIC_DIR, 'js', 'image-loader.js')
    fs.writeFileSync(jsPath, lazyLoadJS)
    console.log('✅ Generated image lazy loading JavaScript')
    
    // Generate optimized CSS
    const imageCSS = generateImageCSS()
    const cssPath = path.join(STATIC_DIR, 'css', 'images-optimized.css')
    
    // Create css directory if it doesn't exist
    const cssDir = path.dirname(cssPath)
    if (!fs.existsSync(cssDir)) {
      fs.mkdirSync(cssDir, { recursive: true })
    }
    
    fs.writeFileSync(cssPath, imageCSS)
    console.log('✅ Generated optimized image CSS')
    
    // Generate responsive image config
    const responsiveConfig = generateResponsiveImageConfig()
    const configPath = path.join(STATIC_DIR, 'image-config.json')
    fs.writeFileSync(configPath, JSON.stringify(responsiveConfig, null, 2))
    console.log('✅ Generated responsive image configuration')
    
    console.log('\n🎉 Image optimization setup completed!')
    console.log('\nNext steps:')
    console.log('1. Add image-loader.js to the page head')
    console.log('2. Include images-optimized.css in the build')
    console.log('3. Update image references to use data-src and data-webp attributes')
    console.log('4. Consider using an image optimization service like Cloudinary or ImageKit')
    console.log('5. Implement WebP conversion for card templates')
    
  } catch (error) {
    console.error('❌ Image optimization failed:', error.message)
    process.exit(1)
  }
}

// Run optimization
optimizeImages()
