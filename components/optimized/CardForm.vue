<template>
  <section class="col-lg-7 col-xl-6 editor-panel" role="form">
    <div class="editor-container">
      <div class="editor-content">
        <form @submit.prevent="onSubmit" class="card-settings-form">
          
          <!-- Basic Settings -->
          <CardBasicSettings
            v-model="basicSettings"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onBasicSettingsUpdate"
          />
          
          <!-- Card Information -->
          <CardInformation
            v-model="cardInfo"
            :card-type="cardType"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onCardInfoUpdate"
          />
          
          <!-- Monster Settings -->
          <CardMonsterSettings
            v-if="cardType === 'Monster'"
            v-model="monsterSettings"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onMonsterSettingsUpdate"
          />
          
          <!-- Pendulum Settings -->
          <CardPendulumSettings
            v-if="showPendulum"
            v-model="pendulumSettings"
            :ui-lang="uiLang"
            :ui="ui"
            @update="onPendulumSettingsUpdate"
          />
          
          <!-- Card Actions -->
          <CardActions
            :ui-lang="uiLang"
            :ui="ui"
            @generate="onGenerate"
            @download="onDownload"
            @reset="onReset"
          />
          
        </form>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'CardForm',
  
  components: {
    CardBasicSettings: () => import('./CardBasicSettings.vue'),
    CardInformation: () => import('./CardInformation.vue'),
    CardMonsterSettings: () => import('./CardMonsterSettings.vue'),
    CardPendulumSettings: () => import('./CardPendulumSettings.vue'),
    CardActions: () => import('./CardActions.vue')
  },
  
  props: {
    uiLang: {
      type: String,
      default: 'en'
    },
    ui: {
      type: Object,
      default: () => ({})
    },
    cardType: {
      type: String,
      default: 'Monster'
    }
  },
  
  data() {
    return {
      basicSettings: {},
      cardInfo: {},
      monsterSettings: {},
      pendulumSettings: {}
    }
  },
  
  computed: {
    showPendulum() {
      return this.cardType === 'Monster' && this.monsterSettings.pendulum
    }
  },
  
  methods: {
    onSubmit() {
      this.onGenerate()
    },
    
    onBasicSettingsUpdate(settings) {
      this.basicSettings = { ...settings }
      this.$emit('update:basic-settings', this.basicSettings)
    },
    
    onCardInfoUpdate(info) {
      this.cardInfo = { ...info }
      this.$emit('update:card-info', this.cardInfo)
    },
    
    onMonsterSettingsUpdate(settings) {
      this.monsterSettings = { ...settings }
      this.$emit('update:monster-settings', this.monsterSettings)
    },
    
    onPendulumSettingsUpdate(settings) {
      this.pendulumSettings = { ...settings }
      this.$emit('update:pendulum-settings', this.pendulumSettings)
    },
    
    onGenerate() {
      this.$emit('generate-card')
    },
    
    onDownload() {
      this.$emit('download-card')
    },
    
    onReset() {
      this.$emit('reset-card')
    }
  }
}
</script>

<style scoped>
.editor-panel {
  padding: 1rem;
}

.card-settings-form {
  max-width: 100%;
}

@media (max-width: 991px) {
  .editor-panel {
    padding: 0.5rem;
  }
}
</style>