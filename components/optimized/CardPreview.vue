<template>
  <aside class="col-lg-5 col-xl-6 preview-panel" role="complementary">
    <div class="preview-container">
      <div class="card-preview-wrapper">
        <div
          id="yugiohcard-wrap"
          ref="yugiohcard-wrap"
          class="card-display"
          @mousemove="move"
          @mouseleave="leave"
          role="img"
          :aria-label="cardTitle || 'Yu-Gi-Oh! Card Preview'"
        >
          <canvas
            id="yugiohcard"
            ref="yugiohcard"
            class="card-canvas"
            :aria-label="`${cardTitle || 'Untitled Card'} - ${cardType} Card`"
          ></canvas>
        </div>
        
        <!-- Preview Controls -->
        <div class="preview-controls mt-3">
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary me-2"
            @click="resetCardRotation"
            :aria-label="$t('controls.resetRotation')"
          >
            <fa :icon="['fas', 'undo']" />
          </button>
          
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary me-2"
            @click="toggleFullscreen"
            :aria-label="$t('controls.fullscreen')"
          >
            <fa :icon="['fas', 'expand']" />
          </button>
          
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary"
            @click="copyCardImage"
            :aria-label="$t('controls.copy')"
          >
            <fa :icon="['fas', 'copy']" />
          </button>
        </div>
      </div>
    </div>
  </aside>
</template>

<script>
export default {
  name: 'CardPreview',
  
  props: {
    cardTitle: {
      type: String,
      default: ''
    },
    cardType: {
      type: String,
      default: 'Monster'
    }
  },
  
  methods: {
    move(event) {
      this.$emit('card-move', event)
    },
    
    leave(event) {
      this.$emit('card-leave', event)
    },
    
    resetCardRotation() {
      this.$emit('reset-rotation')
    },
    
    toggleFullscreen() {
      this.$emit('toggle-fullscreen')
    },
    
    copyCardImage() {
      this.$emit('copy-image')
    }
  }
}
</script>

<style scoped>
.preview-panel {
  position: sticky;
  top: 80px;
  height: fit-content;
}

.card-display {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.1s ease-out;
}

.card-canvas {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.preview-controls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

@media (max-width: 991px) {
  .preview-panel {
    position: static;
    margin-bottom: 2rem;
  }
}
</style>