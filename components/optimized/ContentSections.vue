<template>
  <div class="content-sections">
    <!-- Features Section -->
    <section 
      id="features" 
      class="features-section py-5 anchor-section" 
      role="region"
      v-intersection="onFeaturesVisible"
    >
      <FeaturesSection 
        v-if="featuresLoaded"
        :features-data="featuresData"
      />
      <div v-else class="section-placeholder">
        <div class="loading-spinner"></div>
      </div>
    </section>

    <!-- How to Use Section -->
    <section 
      id="how-to-use" 
      class="how-to-use-section py-5 anchor-section" 
      role="region"
      v-intersection="onHowToUseVisible"
    >
      <HowToUseSection 
        v-if="howToUseLoaded"
        :steps-data="stepsData"
      />
      <div v-else class="section-placeholder">
        <div class="loading-spinner"></div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section 
      id="faq" 
      class="faq-section py-5 anchor-section" 
      role="region"
      v-intersection="onFaqVisible"
    >
      <FAQSection 
        v-if="faqLoaded"
        :faq-data="faqData"
      />
      <div v-else class="section-placeholder">
        <div class="loading-spinner"></div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'ContentSections',
  
  components: {
    FeaturesSection: () => import('~/components/FeaturesSection.vue'),
    HowToUseSection: () => import('~/components/HowToUseSection.vue'),
    FAQSection: () => import('~/components/FAQSection.vue')
  },
  
  directives: {
    intersection: {
      inserted(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value()
              observer.unobserve(el)
            }
          })
        }, {
          rootMargin: '100px 0px',
          threshold: 0.1
        })
        
        observer.observe(el)
      }
    }
  },
  
  props: {
    featuresData: {
      type: Array,
      default: () => []
    },
    stepsData: {
      type: Array,
      default: () => []
    },
    faqData: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      featuresLoaded: false,
      howToUseLoaded: false,
      faqLoaded: false
    }
  },
  
  methods: {
    onFeaturesVisible() {
      if (!this.featuresLoaded) {
        this.featuresLoaded = true
      }
    },
    
    onHowToUseVisible() {
      if (!this.howToUseLoaded) {
        this.howToUseLoaded = true
      }
    },
    
    onFaqVisible() {
      if (!this.faqLoaded) {
        this.faqLoaded = true
      }
    }
  }
}
</script>

<style scoped>
.section-placeholder {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>